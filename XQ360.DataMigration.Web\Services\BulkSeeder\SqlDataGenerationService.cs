using Microsoft.Data.SqlClient;
using Microsoft.Extensions.Logging;
using Microsoft.Extensions.Options;
using System.Data;
using XQ360.DataMigration.Web.Models;
using XQ360.DataMigration.Web.Services.BulkSeeder;
using XQ360.DataMigration.Services;

namespace XQ360.DataMigration.Web.Services.BulkSeeder;

/// <summary>
/// SQL-based data generation service integrated with existing migration infrastructure
/// </summary>
public class SqlDataGenerationService : ISqlDataGenerationService
{
    private readonly ILogger<SqlDataGenerationService> _logger;
    private readonly BulkSeederConfiguration _options;
    private readonly IEnvironmentConfigurationService _environmentService;

    public SqlDataGenerationService(
        ILogger<SqlDataGenerationService> logger,
        IOptions<BulkSeederConfiguration> options,
        IEnvironmentConfigurationService environmentService)
    {
        _logger = logger ?? throw new ArgumentNullException(nameof(logger));
        _options = options?.Value ?? throw new ArgumentNullException(nameof(options));
        _environmentService = environmentService ?? throw new ArgumentNullException(nameof(environmentService));
    }

    public async Task<Guid> CreateSeederSessionAsync(string sessionName, CancellationToken cancellationToken = default)
    {
        var sessionId = Guid.NewGuid();

        using var connection = new SqlConnection(_environmentService.CurrentMigrationConfiguration.DatabaseConnection);
        await connection.OpenAsync(cancellationToken);

        const string sql = @"
            IF NOT EXISTS (SELECT * FROM sys.schemas WHERE name = 'Staging')
            BEGIN
                EXEC('CREATE SCHEMA [Staging]')
            END

            IF NOT EXISTS (SELECT * FROM sys.objects WHERE object_id = OBJECT_ID(N'[Staging].[SeederSession]') AND type in (N'U'))
            BEGIN
                CREATE TABLE [Staging].[SeederSession] (
                    [Id] UNIQUEIDENTIFIER PRIMARY KEY,
                    [SessionName] NVARCHAR(255) NOT NULL,
                    [StartTime] DATETIME2 NOT NULL,
                    [EndTime] DATETIME2 NULL,
                    [Status] NVARCHAR(50) NOT NULL,
                    [TotalRows] INT NOT NULL DEFAULT 0,
                    [SuccessfulRows] INT NOT NULL DEFAULT 0,
                    [FailedRows] INT NOT NULL DEFAULT 0,
                    [CreatedBy] NVARCHAR(100) NOT NULL,
                    [Environment] NVARCHAR(50) NOT NULL
                )
            END

            INSERT INTO [Staging].[SeederSession] 
            ([Id], [SessionName], [StartTime], [Status], [CreatedBy], [Environment])
            VALUES (@SessionId, @SessionName, @StartTime, @Status, @CreatedBy, @Environment)";

        using var command = new SqlCommand(sql, connection);
        command.Parameters.AddWithValue("@SessionId", sessionId);
        command.Parameters.AddWithValue("@SessionName", sessionName);
        command.Parameters.AddWithValue("@StartTime", DateTime.UtcNow);
        command.Parameters.AddWithValue("@Status", "Running");
        command.Parameters.AddWithValue("@CreatedBy", Environment.UserName);
        command.Parameters.AddWithValue("@Environment", _environmentService.CurrentEnvironmentKey);

        await command.ExecuteNonQueryAsync(cancellationToken);

        _logger.LogInformation("Created seeding session {SessionId} with name '{SessionName}' in environment {Environment}",
            sessionId, sessionName, _environmentService.CurrentEnvironmentKey);
        return sessionId;
    }

    public async Task<DataGenerationResult> GenerateDriverDataAsync(Guid sessionId, int count, CancellationToken cancellationToken = default)
    {
        var startTime = DateTime.UtcNow;
        var result = new DataGenerationResult();

        try
        {
            _logger.LogInformation("Generating {Count} driver records for session {SessionId}", count, sessionId);

            using var connection = new SqlConnection(_environmentService.CurrentMigrationConfiguration.DatabaseConnection);
            await connection.OpenAsync(cancellationToken);

            // Create temporary staging table for drivers if it doesn't exist
            await CreateDriverStagingTableAsync(connection, cancellationToken);

            // Generate driver data in batches using SQL
            var batchSize = _options.TempTableBatchSize;
            var totalGenerated = 0;

            for (int offset = 0; offset < count; offset += batchSize)
            {
                var currentBatchSize = Math.Min(batchSize, count - offset);
                await GenerateDriverBatchAsync(connection, sessionId, offset, currentBatchSize, cancellationToken);
                totalGenerated += currentBatchSize;

                _logger.LogInformation("Generated {CurrentGenerated}/{TotalCount} driver records", totalGenerated, count);
            }

            result.Success = true;
            result.GeneratedRows = totalGenerated;
            result.Duration = DateTime.UtcNow - startTime;
            result.Summary = $"Successfully generated {totalGenerated} driver records";

            _logger.LogInformation("Driver data generation completed for session {SessionId}. Generated {Count} records in {Duration}",
                sessionId, totalGenerated, result.Duration);
        }
        catch (Exception ex)
        {
            result.Success = false;
            result.Duration = DateTime.UtcNow - startTime;
            result.Errors.Add(ex.Message);
            result.Summary = $"Driver data generation failed: {ex.Message}";

            _logger.LogError(ex, "Driver data generation failed for session {SessionId}", sessionId);
        }

        return result;
    }

    public async Task<DataGenerationResult> GenerateVehicleDataAsync(Guid sessionId, int count, CancellationToken cancellationToken = default)
    {
        var startTime = DateTime.UtcNow;
        var result = new DataGenerationResult();

        try
        {
            _logger.LogInformation("Generating {Count} vehicle records for session {SessionId}", count, sessionId);

            using var connection = new SqlConnection(_environmentService.CurrentMigrationConfiguration.DatabaseConnection);
            await connection.OpenAsync(cancellationToken);

            // Create temporary staging table for vehicles if it doesn't exist
            await CreateVehicleStagingTableAsync(connection, cancellationToken);

            // Generate vehicle data in batches using SQL
            var batchSize = _options.TempTableBatchSize;
            var totalGenerated = 0;

            for (int offset = 0; offset < count; offset += batchSize)
            {
                var currentBatchSize = Math.Min(batchSize, count - offset);
                await GenerateVehicleBatchAsync(connection, sessionId, offset, currentBatchSize, cancellationToken);
                totalGenerated += currentBatchSize;

                _logger.LogInformation("Generated {CurrentGenerated}/{TotalCount} vehicle records", totalGenerated, count);
            }

            result.Success = true;
            result.GeneratedRows = totalGenerated;
            result.Duration = DateTime.UtcNow - startTime;
            result.Summary = $"Successfully generated {totalGenerated} vehicle records";

            _logger.LogInformation("Vehicle data generation completed for session {SessionId}. Generated {Count} records in {Duration}",
                sessionId, totalGenerated, result.Duration);
        }
        catch (Exception ex)
        {
            result.Success = false;
            result.Duration = DateTime.UtcNow - startTime;
            result.Errors.Add(ex.Message);
            result.Summary = $"Vehicle data generation failed: {ex.Message}";

            _logger.LogError(ex, "Vehicle data generation failed for session {SessionId}", sessionId);
        }

        return result;
    }

    public async Task UpdateSeederSessionAsync(Guid sessionId, string status, int totalRows, int successfulRows, int failedRows, CancellationToken cancellationToken = default)
    {
        try
        {
            using var connection = new SqlConnection(_environmentService.CurrentMigrationConfiguration.DatabaseConnection);
            await connection.OpenAsync(cancellationToken);

            const string sql = @"
                UPDATE [Staging].[SeederSession] 
                SET [Status] = @Status,
                    [EndTime] = @EndTime,
                    [TotalRows] = @TotalRows,
                    [SuccessfulRows] = @SuccessfulRows,
                    [FailedRows] = @FailedRows
                WHERE [Id] = @SessionId";

            using var command = new SqlCommand(sql, connection);
            command.Parameters.AddWithValue("@SessionId", sessionId);
            command.Parameters.AddWithValue("@Status", status);
            command.Parameters.AddWithValue("@EndTime", DateTime.UtcNow);
            command.Parameters.AddWithValue("@TotalRows", totalRows);
            command.Parameters.AddWithValue("@SuccessfulRows", successfulRows);
            command.Parameters.AddWithValue("@FailedRows", failedRows);

            await command.ExecuteNonQueryAsync(cancellationToken);

            _logger.LogInformation("Updated seeding session {SessionId} status to {Status}", sessionId, status);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Failed to update seeding session {SessionId}", sessionId);
            throw;
        }
    }

    public async Task<ValidationResult> ValidateStagedDataAsync(Guid sessionId, CancellationToken cancellationToken = default)
    {
        var result = new ValidationResult();

        try
        {
            using var connection = new SqlConnection(_environmentService.CurrentMigrationConfiguration.DatabaseConnection);
            await connection.OpenAsync(cancellationToken);

            // Perform basic validation checks on staged data
            const string validationSql = @"
                DECLARE @DriverCount INT = 0, @VehicleCount INT = 0, @InvalidDrivers INT = 0, @InvalidVehicles INT = 0

                -- Count staged drivers
                IF OBJECT_ID('[Staging].[DriverStaging]') IS NOT NULL
                BEGIN
                    SELECT @DriverCount = COUNT(*) FROM [Staging].[DriverStaging] WHERE [SessionId] = @SessionId
                    SELECT @InvalidDrivers = COUNT(*) FROM [Staging].[DriverStaging] 
                    WHERE [SessionId] = @SessionId AND ([FirstName] IS NULL OR [LastName] IS NULL OR [Email] IS NULL)
                END

                -- Count staged vehicles  
                IF OBJECT_ID('[Staging].[VehicleStaging]') IS NOT NULL
                BEGIN
                    SELECT @VehicleCount = COUNT(*) FROM [Staging].[VehicleStaging] WHERE [SessionId] = @SessionId
                    SELECT @InvalidVehicles = COUNT(*) FROM [Staging].[VehicleStaging] 
                    WHERE [SessionId] = @SessionId AND ([VehicleName] IS NULL OR [DeviceId] IS NULL)
                END

                SELECT 
                    @DriverCount AS DriverCount, 
                    @VehicleCount AS VehicleCount,
                    @InvalidDrivers AS InvalidDrivers,
                    @InvalidVehicles AS InvalidVehicles";

            using var command = new SqlCommand(validationSql, connection);
            command.Parameters.AddWithValue("@SessionId", sessionId);

            using var reader = await command.ExecuteReaderAsync(cancellationToken);
            if (await reader.ReadAsync())
            {
                var driverCount = reader.GetInt32("DriverCount");
                var vehicleCount = reader.GetInt32("VehicleCount");
                var invalidDrivers = reader.GetInt32("InvalidDrivers");
                var invalidVehicles = reader.GetInt32("InvalidVehicles");

                result.ValidRows = (driverCount - invalidDrivers) + (vehicleCount - invalidVehicles);
                result.InvalidRows = invalidDrivers + invalidVehicles;

                if (invalidDrivers > 0)
                {
                    result.ValidationErrors.Add($"{invalidDrivers} drivers have missing required fields");
                }

                if (invalidVehicles > 0)
                {
                    result.ValidationErrors.Add($"{invalidVehicles} vehicles have missing required fields");
                }

                result.Success = result.InvalidRows == 0;
                result.Summary = result.Success ?
                    $"Validation passed: {result.ValidRows} valid records" :
                    $"Validation failed: {result.InvalidRows} invalid records found";
            }

            _logger.LogInformation("Validation completed for session {SessionId}: {ValidRows} valid, {InvalidRows} invalid",
                sessionId, result.ValidRows, result.InvalidRows);
        }
        catch (Exception ex)
        {
            result.Success = false;
            result.ValidationErrors.Add(ex.Message);
            result.Summary = $"Validation failed: {ex.Message}";
            _logger.LogError(ex, "Validation failed for session {SessionId}", sessionId);
        }

        return result;
    }

    public async Task<ProcessingResult> ProcessStagedDataAsync(Guid sessionId, bool dryRun = false, CancellationToken cancellationToken = default)
    {
        var result = new ProcessingResult();

        try
        {
            using var connection = new SqlConnection(_environmentService.CurrentMigrationConfiguration.DatabaseConnection);
            await connection.OpenAsync(cancellationToken);

            if (dryRun)
            {
                // For dry run, just count what would be processed
                result.ProcessedRows = await CountStagedRecordsAsync(connection, sessionId, cancellationToken);
                result.Success = true;
                result.Summary = $"Dry run: {result.ProcessedRows} records would be processed";
            }
            else
            {
                // Process actual data (implementation would depend on specific business requirements)
                // For now, this is a placeholder that marks the staging data as processed
                await MarkStagingDataAsProcessedAsync(connection, sessionId, cancellationToken);

                result.ProcessedRows = await CountStagedRecordsAsync(connection, sessionId, cancellationToken);
                result.InsertedRows = result.ProcessedRows; // Simplified - all records are "inserted"
                result.Success = true;
                result.Summary = $"Processing completed: {result.InsertedRows} records processed";
            }

            _logger.LogInformation("Processing completed for session {SessionId}: {ProcessedRows} records",
                sessionId, result.ProcessedRows);
        }
        catch (Exception ex)
        {
            result.Success = false;
            result.ProcessingErrors.Add(ex.Message);
            result.Summary = $"Processing failed: {ex.Message}";
            _logger.LogError(ex, "Processing failed for session {SessionId}", sessionId);
        }

        return result;
    }

    private async Task CreateDriverStagingTableAsync(SqlConnection connection, CancellationToken cancellationToken)
    {
        const string sql = @"
            IF NOT EXISTS (SELECT * FROM sys.objects WHERE object_id = OBJECT_ID(N'[Staging].[DriverStaging]') AND type in (N'U'))
            BEGIN
                CREATE TABLE [Staging].[DriverStaging] (
                    [Id] UNIQUEIDENTIFIER PRIMARY KEY DEFAULT NEWID(),
                    [SessionId] UNIQUEIDENTIFIER NOT NULL,
                    [FirstName] NVARCHAR(100) NOT NULL,
                    [LastName] NVARCHAR(100) NOT NULL,
                    [Email] NVARCHAR(255) NOT NULL,
                    [Phone] NVARCHAR(50),
                    [EmployeeId] NVARCHAR(50),
                    [Department] NVARCHAR(100),
                    [CreatedAt] DATETIME2 DEFAULT GETUTCDATE()
                )
            END";

        using var command = new SqlCommand(sql, connection);
        await command.ExecuteNonQueryAsync(cancellationToken);
    }

    private async Task CreateVehicleStagingTableAsync(SqlConnection connection, CancellationToken cancellationToken)
    {
        const string sql = @"
            IF NOT EXISTS (SELECT * FROM sys.objects WHERE object_id = OBJECT_ID(N'[Staging].[VehicleStaging]') AND type in (N'U'))
            BEGIN
                CREATE TABLE [Staging].[VehicleStaging] (
                    [Id] UNIQUEIDENTIFIER PRIMARY KEY DEFAULT NEWID(),
                    [SessionId] UNIQUEIDENTIFIER NOT NULL,
                    [VehicleName] NVARCHAR(100) NOT NULL,
                    [DeviceId] NVARCHAR(50) NOT NULL,
                    [LicensePlate] NVARCHAR(20),
                    [Make] NVARCHAR(50),
                    [Model] NVARCHAR(50),
                    [Year] INT,
                    [VIN] NVARCHAR(50),
                    [CreatedAt] DATETIME2 DEFAULT GETUTCDATE()
                )
            END";

        using var command = new SqlCommand(sql, connection);
        await command.ExecuteNonQueryAsync(cancellationToken);
    }

    private async Task GenerateDriverBatchAsync(SqlConnection connection, Guid sessionId, int offset, int batchSize, CancellationToken cancellationToken)
    {
        const string sql = @"
            INSERT INTO [Staging].[DriverStaging] ([SessionId], [FirstName], [LastName], [Email], [Phone], [EmployeeId], [Department])
            SELECT 
                @SessionId,
                'Driver' + CAST((ROW_NUMBER() OVER (ORDER BY (SELECT NULL)) + @Offset) AS NVARCHAR(10)),
                'LastName' + CAST((ROW_NUMBER() OVER (ORDER BY (SELECT NULL)) + @Offset) AS NVARCHAR(10)),
                'driver' + CAST((ROW_NUMBER() OVER (ORDER BY (SELECT NULL)) + @Offset) AS NVARCHAR(10)) + '@example.com',
                '******-' + RIGHT('0000' + CAST((ROW_NUMBER() OVER (ORDER BY (SELECT NULL)) + @Offset) AS NVARCHAR(4)), 4),
                'EMP' + RIGHT('0000' + CAST((ROW_NUMBER() OVER (ORDER BY (SELECT NULL)) + @Offset) AS NVARCHAR(4)), 4),
                'Department' + CAST(((ROW_NUMBER() OVER (ORDER BY (SELECT NULL)) + @Offset) % 5 + 1) AS NVARCHAR(2))
            FROM (SELECT TOP (@BatchSize) 1 AS dummy FROM sys.objects o1 CROSS JOIN sys.objects o2) AS numbers";

        using var command = new SqlCommand(sql, connection);
        command.Parameters.AddWithValue("@SessionId", sessionId);
        command.Parameters.AddWithValue("@Offset", offset);
        command.Parameters.AddWithValue("@BatchSize", batchSize);

        await command.ExecuteNonQueryAsync(cancellationToken);
    }

    private async Task GenerateVehicleBatchAsync(SqlConnection connection, Guid sessionId, int offset, int batchSize, CancellationToken cancellationToken)
    {
        const string sql = @"
            INSERT INTO [Staging].[VehicleStaging] ([SessionId], [VehicleName], [DeviceId], [LicensePlate], [Make], [Model], [Year], [VIN])
            SELECT 
                @SessionId,
                'Vehicle-' + CAST((ROW_NUMBER() OVER (ORDER BY (SELECT NULL)) + @Offset) AS NVARCHAR(10)),
                'DEV' + RIGHT('0000' + CAST((ROW_NUMBER() OVER (ORDER BY (SELECT NULL)) + @Offset) AS NVARCHAR(4)), 4),
                'ABC' + RIGHT('000' + CAST((ROW_NUMBER() OVER (ORDER BY (SELECT NULL)) + @Offset) AS NVARCHAR(3)), 3),
                CASE ((ROW_NUMBER() OVER (ORDER BY (SELECT NULL)) + @Offset) % 4)
                    WHEN 0 THEN 'Ford' WHEN 1 THEN 'Chevrolet' WHEN 2 THEN 'Toyota' ELSE 'Honda' END,
                CASE ((ROW_NUMBER() OVER (ORDER BY (SELECT NULL)) + @Offset) % 4)
                    WHEN 0 THEN 'F-150' WHEN 1 THEN 'Silverado' WHEN 2 THEN 'Camry' ELSE 'Civic' END,
                2020 + ((ROW_NUMBER() OVER (ORDER BY (SELECT NULL)) + @Offset) % 5),
                'VIN' + RIGHT('00000000000000' + CAST((ROW_NUMBER() OVER (ORDER BY (SELECT NULL)) + @Offset) AS NVARCHAR(14)), 14)
            FROM (SELECT TOP (@BatchSize) 1 AS dummy FROM sys.objects o1 CROSS JOIN sys.objects o2) AS numbers";

        using var command = new SqlCommand(sql, connection);
        command.Parameters.AddWithValue("@SessionId", sessionId);
        command.Parameters.AddWithValue("@Offset", offset);
        command.Parameters.AddWithValue("@BatchSize", batchSize);

        await command.ExecuteNonQueryAsync(cancellationToken);
    }

    private async Task<int> CountStagedRecordsAsync(SqlConnection connection, Guid sessionId, CancellationToken cancellationToken)
    {
        const string sql = @"
            DECLARE @TotalCount INT = 0

            IF OBJECT_ID('[Staging].[DriverStaging]') IS NOT NULL
                SELECT @TotalCount = @TotalCount + COUNT(*) FROM [Staging].[DriverStaging] WHERE [SessionId] = @SessionId

            IF OBJECT_ID('[Staging].[VehicleStaging]') IS NOT NULL
                SELECT @TotalCount = @TotalCount + COUNT(*) FROM [Staging].[VehicleStaging] WHERE [SessionId] = @SessionId

            SELECT @TotalCount";

        using var command = new SqlCommand(sql, connection);
        command.Parameters.AddWithValue("@SessionId", sessionId);

        var result = await command.ExecuteScalarAsync(cancellationToken);
        return Convert.ToInt32(result ?? 0);
    }

    private async Task MarkStagingDataAsProcessedAsync(SqlConnection connection, Guid sessionId, CancellationToken cancellationToken)
    {
        using var transaction = connection.BeginTransaction();

        try
        {
            var processedRows = 0;

            // Process Driver records - Insert into Person and Driver tables
            if (await TableExistsAsync(connection, "[Staging].[DriverStaging]", transaction))
            {
                processedRows += await ProcessDriverStagingDataAsync(connection, sessionId, transaction, cancellationToken);
            }

            // Process Vehicle records - Insert into Vehicle table
            if (await TableExistsAsync(connection, "[Staging].[VehicleStaging]", transaction))
            {
                processedRows += await ProcessVehicleStagingDataAsync(connection, sessionId, transaction, cancellationToken);
            }

            await transaction.CommitAsync(cancellationToken);

            _logger.LogInformation("Successfully processed {ProcessedRows} records from staging to production tables for session {SessionId}",
                processedRows, sessionId);
        }
        catch (Exception ex)
        {
            await transaction.RollbackAsync(cancellationToken);
            _logger.LogError(ex, "Failed to process staging data for session {SessionId}. Transaction rolled back.", sessionId);
            throw;
        }
    }

    private static async Task<bool> TableExistsAsync(SqlConnection connection, string tableName, SqlTransaction transaction)
    {
        const string sql = "SELECT CASE WHEN OBJECT_ID(@TableName) IS NOT NULL THEN 1 ELSE 0 END";
        using var command = new SqlCommand(sql, connection, transaction);
        command.Parameters.AddWithValue("@TableName", tableName);
        var result = await command.ExecuteScalarAsync();
        return Convert.ToBoolean(result);
    }

    private async Task<int> ProcessDriverStagingDataAsync(SqlConnection connection, Guid sessionId, SqlTransaction transaction, CancellationToken cancellationToken)
    {
        // First, get required foreign key IDs (Customer, Site, Department)
        // This is a simplified implementation - you may need to adjust based on your business logic
        var foreignKeysSql = @"
            SELECT TOP 1
                c.Id as CustomerId,
                s.Id as SiteId,
                d.Id as DepartmentId
            FROM Customer c
            CROSS JOIN Site s
            CROSS JOIN Department d
            WHERE c.Active = 1 AND s.Active = 1 AND d.Active = 1";

        Guid customerId, siteId, departmentId;
        using (var fkCommand = new SqlCommand(foreignKeysSql, connection, transaction))
        {
            using var reader = await fkCommand.ExecuteReaderAsync(cancellationToken);
            if (!await reader.ReadAsync(cancellationToken))
            {
                throw new InvalidOperationException("No active Customer, Site, or Department found. Cannot process driver data.");
            }
            customerId = reader.GetGuid("CustomerId");
            siteId = reader.GetGuid("SiteId");
            departmentId = reader.GetGuid("DepartmentId");
        }

        // Insert drivers into Person and Driver tables
        const string insertDriversSql = @"
            WITH StagedDrivers AS (
                SELECT
                    ds.FirstName,
                    ds.LastName,
                    ds.Email,
                    ds.Phone,
                    ds.EmployeeId,
                    ds.Department,
                    NEWID() as PersonId,
                    NEWID() as DriverId
                FROM [Staging].[DriverStaging] ds
                WHERE ds.SessionId = @SessionId
                AND NOT EXISTS (
                    SELECT 1 FROM Person p
                    WHERE p.FirstName = ds.FirstName
                    AND p.LastName = ds.LastName
                    AND p.Email = ds.Email
                )
            )
            INSERT INTO Person (
                Id, FirstName, LastName, Email, Phone,
                CustomerId, SiteId, DepartmentId,
                Active, IsDriver, IsActiveDriver, VehicleAccess,
                WebsiteAccess, NormalDriverAccess, CanUnlockVehicle, -- Removed SendDenyMessage
                Created, Updated, CreatedBy, UpdatedBy
            )
            OUTPUT INSERTED.Id, INSERTED.FirstName, INSERTED.LastName
            SELECT
                sd.PersonId, sd.FirstName, sd.LastName, sd.Email, sd.Phone,
                @CustomerId, @SiteId, @DepartmentId,
                1, 1, 1, 1, -- Active, IsDriver, IsActiveDriver, VehicleAccess
                0, 1, 1, -- WebsiteAccess, NormalDriverAccess, CanUnlockVehicle (removed SendDenyMessage)
                GETUTCDATE(), GETUTCDATE(), 'BulkSeeder', 'BulkSeeder'
            FROM StagedDrivers sd;

            -- Insert corresponding Driver records
            WITH StagedDrivers AS (
                SELECT
                    ds.FirstName,
                    ds.LastName,
                    ds.Email,
                    p.Id as PersonId,
                    NEWID() as DriverId
                FROM [Staging].[DriverStaging] ds
                INNER JOIN Person p ON ds.FirstName = p.FirstName
                    AND ds.LastName = p.LastName
                    AND ds.Email = p.Email
                WHERE ds.SessionId = @SessionId
                AND NOT EXISTS (SELECT 1 FROM Driver d WHERE d.PersonId = p.Id)
            )
            INSERT INTO Driver (
                Id, PersonId, IsActiveDriver, VehicleAccess, OnDemand, MaintenanceMode,
                Created, Updated, CreatedBy, UpdatedBy
            )
            SELECT
                sd.DriverId, sd.PersonId, 1, 1, 0, 0,
                GETUTCDATE(), GETUTCDATE(), 'BulkSeeder', 'BulkSeeder'
            FROM StagedDrivers sd;

            -- Mark staging records as processed
            UPDATE [Staging].[DriverStaging]
            SET CreatedAt = GETUTCDATE()
            WHERE SessionId = @SessionId;

            -- Return count of processed records
            SELECT COUNT(*) FROM [Staging].[DriverStaging] WHERE SessionId = @SessionId";

        using var command = new SqlCommand(insertDriversSql, connection, transaction);
        command.Parameters.AddWithValue("@SessionId", sessionId);
        command.Parameters.AddWithValue("@CustomerId", customerId);
        command.Parameters.AddWithValue("@SiteId", siteId);
        command.Parameters.AddWithValue("@DepartmentId", departmentId);

        var result = await command.ExecuteScalarAsync(cancellationToken);
        return Convert.ToInt32(result ?? 0);
    }

    private async Task<int> ProcessVehicleStagingDataAsync(SqlConnection connection, Guid sessionId, SqlTransaction transaction, CancellationToken cancellationToken)
    {
        // Get required foreign key IDs for vehicles
        var foreignKeysSql = @"
            SELECT TOP 1
                c.Id as CustomerId,
                s.Id as SiteId,
                m.Id as ModelId
            FROM Customer c
            CROSS JOIN Site s
            CROSS JOIN Model m
            WHERE c.Active = 1 AND s.Active = 1";

        Guid customerId, siteId, modelId;
        using (var fkCommand = new SqlCommand(foreignKeysSql, connection, transaction))
        {
            using var reader = await fkCommand.ExecuteReaderAsync(cancellationToken);
            if (!await reader.ReadAsync(cancellationToken))
            {
                throw new InvalidOperationException("No active Customer, Site, or Model found. Cannot process vehicle data.");
            }
            customerId = reader.GetGuid("CustomerId");
            siteId = reader.GetGuid("SiteId");
            modelId = reader.GetGuid("ModelId");
        }

        // Insert vehicles into Vehicle table
        const string insertVehiclesSql = @"
            INSERT INTO Vehicle (
                Id, SerialNo, HireNo, Description,
                CustomerId, SiteId, ModelId,
                Active, Created, Updated, CreatedBy, UpdatedBy
            )
            SELECT
                NEWID(),
                vs.DeviceId,
                vs.VehicleName,
                CONCAT(vs.Make, ' ', vs.Model, ' (', vs.Year, ')'),
                @CustomerId, @SiteId, @ModelId,
                1, GETUTCDATE(), GETUTCDATE(), 'BulkSeeder', 'BulkSeeder'
            FROM [Staging].[VehicleStaging] vs
            WHERE vs.SessionId = @SessionId
            AND NOT EXISTS (
                SELECT 1 FROM Vehicle v
                WHERE v.SerialNo = vs.DeviceId OR v.HireNo = vs.VehicleName
            );

            -- Mark staging records as processed
            UPDATE [Staging].[VehicleStaging]
            SET CreatedAt = GETUTCDATE()
            WHERE SessionId = @SessionId;

            -- Return count of processed records
            SELECT COUNT(*) FROM [Staging].[VehicleStaging] WHERE SessionId = @SessionId";

        using var command = new SqlCommand(insertVehiclesSql, connection, transaction);
        command.Parameters.AddWithValue("@SessionId", sessionId);
        command.Parameters.AddWithValue("@CustomerId", customerId);
        command.Parameters.AddWithValue("@SiteId", siteId);
        command.Parameters.AddWithValue("@ModelId", modelId);

        var result = await command.ExecuteScalarAsync(cancellationToken);
        return Convert.ToInt32(result ?? 0);
    }
}
